# Qwen-VL-Plus Go客户端

这是一个用Go语言编写的阿里云DashScope Qwen-VL-Plus多模态生成服务客户端，专门用于识别图片中的考题并返回结构化的JSON数据。

## 功能特性

- 🔍 **图片题目识别**: 识别图片中的考题内容
- 📝 **结构化输出**: 返回包含题目类型、序号、正文和选项的JSON格式数据
- 🛡️ **错误处理**: 完善的错误处理机制
- ⚡ **易于使用**: 简单的API接口，易于集成到其他项目中
- 🔧 **可配置**: 支持自定义提示词和参数配置

## 项目结构

```
.
├── qwen_vl_client.go    # 主要的客户端实现
├── example_usage.go     # 使用示例
├── go.mod              # Go模块文件
└── README.md           # 说明文档
```

## 快速开始

### 1. 基本使用

```go
package main

import (
    "fmt"
    "log"
)

func main() {
    // 创建客户端实例
    client := NewQwenVLClient("your-api-key-here")
    
    // 识别图片中的题目
    imageURL := "http://example.com/question-image.jpg"
    result, err := client.RequestQwenVLPlus(imageURL, "")
    if err != nil {
        log.Fatal(err)
    }
    
    // 打印结果
    fmt.Printf("题目类型: %s\n", result.Type)
    fmt.Printf("题目序号: %s\n", result.Num)
    fmt.Printf("题目正文: %s\n", result.Text)
    for key, value := range result.Options {
        fmt.Printf("选项 %s: %s\n", key, value)
    }
}
```

### 2. 自定义提示词

```go
customPrompt := "请仔细识别这道数学题，包括所有公式和选项"
result, err := client.RequestQwenVLPlus(imageURL, customPrompt)
```

### 3. 批量处理

```go
imageURLs := []string{
    "http://example.com/q1.jpg",
    "http://example.com/q2.jpg",
    "http://example.com/q3.jpg",
}

for i, imageURL := range imageURLs {
    result, err := client.RequestQwenVLPlus(imageURL, "")
    if err != nil {
        log.Printf("处理第%d张图片失败: %v", i+1, err)
        continue
    }
    // 处理结果...
}
```

## API 参考

### QwenVLClient

主要的客户端结构体。

#### NewQwenVLClient(apiKey string) *QwenVLClient

创建新的客户端实例。

**参数:**
- `apiKey`: 阿里云DashScope API密钥

**返回:**
- `*QwenVLClient`: 客户端实例

#### RequestQwenVLPlus(imageURL string, customPrompt string) (*QuestionResult, error)

请求Qwen-VL-Plus进行图片识别。

**参数:**
- `imageURL`: 图片URL地址
- `customPrompt`: 自定义提示词，如果为空则使用默认提示词

**返回:**
- `*QuestionResult`: 识别结果
- `error`: 错误信息

### QuestionResult

题目识别结果结构体。

```go
type QuestionResult struct {
    Type    string            `json:"type"`    // 问题类型
    Num     string            `json:"num"`     // 序号
    Text    string            `json:"text"`    // 题目正文
    Options map[string]string `json:"options"` // 选项
}
```

## 配置说明

### 默认参数

客户端使用以下默认参数：

- **模型**: qwen-vl-plus
- **温度**: 0.2 (较低的随机性，更准确的结果)
- **Top-K**: 1 (只考虑最可能的词)
- **Top-P**: 0.01 (核采样参数)
- **响应格式**: JSON对象
- **超时时间**: 30秒

### 系统提示词

默认系统提示词：
```
精准识别考题,严格标准返回json格式。示例{"type":"问题类型","num":"序号","text":"题目正文","options":{"选项标识":"选项内容"}}
```

## 错误处理

客户端提供了完善的错误处理：

- 网络请求错误
- JSON序列化/反序列化错误
- API响应错误
- 超时错误

所有错误都会返回详细的错误信息，便于调试和日志记录。

## 运行示例

```bash
# 运行主程序
go run qwen_vl_client.go

# 运行使用示例
go run example_usage.go
```

## 注意事项

1. **API密钥安全**: 请妥善保管您的API密钥，不要在代码中硬编码
2. **图片URL**: 确保图片URL可以公开访问
3. **网络连接**: 确保网络连接正常，API调用需要访问外网
4. **配额限制**: 注意API调用的配额和频率限制

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
