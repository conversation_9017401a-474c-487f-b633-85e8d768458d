package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// 请求结构体定义
type QwenVLRequest struct {
	Input      Input      `json:"input"`
	Model      string     `json:"model"`
	Parameters Parameters `json:"parameters"`
}

type Input struct {
	Messages []Message `json:"messages"`
}

type Message struct {
	Content []Content `json:"content"`
	Role    string    `json:"role"`
}

type Content struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

type Parameters struct {
	PresencePenalty    float64        `json:"presence_penalty"`
	RepetitionPenalty  float64        `json:"repetition_penalty"`
	ResponseFormat     ResponseFormat `json:"response_format"`
	Temperature        float64        `json:"temperature"`
	TopK               int            `json:"top_k"`
	TopP               float64        `json:"top_p"`
}

type ResponseFormat struct {
	Type string `json:"type"`
}

// 响应结构体定义
type QwenVLResponse struct {
	Output    Output `json:"output"`
	Usage     Usage  `json:"usage"`
	RequestID string `json:"request_id"`
}

type Output struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	FinishReason string  `json:"finish_reason"`
	Message      Message `json:"message"`
}

type Usage struct {
	OutputTokens int `json:"output_tokens"`
	InputTokens  int `json:"input_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// 题目识别结果结构体
type QuestionResult struct {
	Type    string            `json:"type"`
	Num     string            `json:"num"`
	Text    string            `json:"text"`
	Options map[string]string `json:"options"`
}

// QwenVLClient 客户端结构体
type QwenVLClient struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// NewQwenVLClient 创建新的客户端实例
func NewQwenVLClient(apiKey string) *QwenVLClient {
	return &QwenVLClient{
		APIKey:  apiKey,
		BaseURL: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
		Client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// RequestQwenVLPlus 请求qwen-vl-plus进行图片识别
func (c *QwenVLClient) RequestQwenVLPlus(imageURL string, customPrompt string) (*QuestionResult, error) {
	systemPrompt := "精准识别考题,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"题目正文\",\"options\":{\"选项标识\":\"选项内容\"}}"

	userPrompt := "完整且精准的识别"
	if customPrompt != "" {
		userPrompt = customPrompt
	}

	request := QwenVLRequest{
		Input: Input{
			Messages: []Message{
				{
					Content: []Content{
						{Text: systemPrompt},
					},
					Role: "system",
				},
				{
					Content: []Content{
						{Image: imageURL},
						{Text: userPrompt},
					},
					Role: "user",
				},
			},
		},
		Model: "qwen-vl-plus",
		Parameters: Parameters{
			PresencePenalty:   1.0,
			RepetitionPenalty: 1.0,
			ResponseFormat: ResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
		},
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIKey)

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	var qwenResponse QwenVLResponse
	if err := json.Unmarshal(responseBody, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("响应中没有选择项")
	}

	choice := qwenResponse.Output.Choices[0]
	if len(choice.Message.Content) == 0 {
		return nil, fmt.Errorf("响应消息内容为空")
	}

	var questionResult QuestionResult
	contentText := choice.Message.Content[0].Text
	if err := json.Unmarshal([]byte(contentText), &questionResult); err != nil {
		return nil, fmt.Errorf("解析题目识别结果失败: %v, 原始内容: %s", err, contentText)
	}

	return &questionResult, nil
}

// 使用示例：如何在其他地方调用封装好的方法
func ExampleUsage() {
	// 1. 创建客户端实例
	client := NewQwenVLClient("sk-3920274bedf642c2b7495f534aadca84")

	// 2. 示例1：使用默认提示词识别图片
	fmt.Println("=== 示例1：默认提示词识别 ===")
	imageURL1 := "http://img.igmdns.com/img/ca0726.jpg"
	result1, err := client.RequestQwenVLPlus(imageURL1, "")
	if err != nil {
		log.Printf("识别失败: %v", err)
	} else {
		printQuestionResult("默认识别", result1)
	}

	// 3. 示例2：使用自定义提示词识别图片
	fmt.Println("\n=== 示例2：自定义提示词识别 ===")
	customPrompt := "请仔细识别这道题目，包括题目类型、序号、正文内容和所有选项"
	result2, err := client.RequestQwenVLPlus(imageURL1, customPrompt)
	if err != nil {
		log.Printf("识别失败: %v", err)
	} else {
		printQuestionResult("自定义识别", result2)
	}

	// 4. 示例3：批量处理多张图片
	fmt.Println("\n=== 示例3：批量处理 ===")
	imageURLs := []string{
		"http://img.igmdns.com/img/ca0726.jpg",
		// 可以添加更多图片URL
	}

	for i, imageURL := range imageURLs {
		fmt.Printf("\n处理第%d张图片: %s\n", i+1, imageURL)
		result, err := client.RequestQwenVLPlus(imageURL, "")
		if err != nil {
			log.Printf("第%d张图片识别失败: %v", i+1, err)
			continue
		}
		printQuestionResult(fmt.Sprintf("图片%d", i+1), result)
	}
}

// 打印题目识别结果的辅助函数
func printQuestionResult(title string, result *QuestionResult) {
	fmt.Printf("【%s】识别结果:\n", title)
	fmt.Printf("  题目类型: %s\n", result.Type)
	fmt.Printf("  题目序号: %s\n", result.Num)
	fmt.Printf("  题目正文: %s\n", result.Text)
	if len(result.Options) > 0 {
		fmt.Printf("  选项:\n")
		for key, value := range result.Options {
			fmt.Printf("    %s: %s\n", key, value)
		}
	} else {
		fmt.Printf("  选项: 无\n")
	}
}

// 业务场景示例：在Web服务中使用
func WebServiceExample(imageURL string) (*QuestionResult, error) {
	// 在实际的Web服务中，你可以这样使用
	client := NewQwenVLClient("sk-3920274bedf642c2b7495f534aadca84")
	
	// 调用封装好的方法
	result, err := client.RequestQwenVLPlus(imageURL, "")
	if err != nil {
		return nil, fmt.Errorf("图片识别服务调用失败: %v", err)
	}
	
	return result, nil
}

// 业务场景示例：在数据处理管道中使用
func DataPipelineExample(imageBatch []string) ([]*QuestionResult, error) {
	client := NewQwenVLClient("sk-3920274bedf642c2b7495f534aadca84")
	
	var results []*QuestionResult
	var errors []error
	
	for _, imageURL := range imageBatch {
		result, err := client.RequestQwenVLPlus(imageURL, "")
		if err != nil {
			errors = append(errors, fmt.Errorf("处理图片 %s 失败: %v", imageURL, err))
			continue
		}
		results = append(results, result)
	}
	
	if len(errors) > 0 {
		// 可以根据业务需求决定如何处理错误
		fmt.Printf("处理过程中出现 %d 个错误\n", len(errors))
		for _, err := range errors {
			fmt.Printf("错误: %v\n", err)
		}
	}
	
	return results, nil
}

func main() {
	// 运行使用示例
	ExampleUsage()
}
