package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 请求结构体定义
type QwenVLRequest struct {
	Input      Input      `json:"input"`
	Model      string     `json:"model"`
	Parameters Parameters `json:"parameters"`
}

type Input struct {
	Messages []Message `json:"messages"`
}

type Message struct {
	Content []Content `json:"content"`
	Role    string    `json:"role"`
}

type Content struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

type Parameters struct {
	PresencePenalty    float64        `json:"presence_penalty"`
	RepetitionPenalty  float64        `json:"repetition_penalty"`
	ResponseFormat     ResponseFormat `json:"response_format"`
	Temperature        float64        `json:"temperature"`
	TopK               int            `json:"top_k"`
	TopP               float64        `json:"top_p"`
}

type ResponseFormat struct {
	Type string `json:"type"`
}

// 响应结构体定义
type QwenVLResponse struct {
	Output    Output `json:"output"`
	Usage     Usage  `json:"usage"`
	RequestID string `json:"request_id"`
}

type Output struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	FinishReason string  `json:"finish_reason"`
	Message      Message `json:"message"`
}

type Usage struct {
	OutputTokens int `json:"output_tokens"`
	InputTokens  int `json:"input_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// 题目识别结果结构体
type QuestionResult struct {
	Type    string            `json:"type"`
	Num     string            `json:"num"`
	Text    string            `json:"text"`
	Options map[string]string `json:"options"`
}

// QwenVLClient 客户端结构体
type QwenVLClient struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// NewQwenVLClient 创建新的客户端实例
func NewQwenVLClient(apiKey string) *QwenVLClient {
	return &QwenVLClient{
		APIKey:  apiKey,
		BaseURL: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
		Client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// RequestQwenVLPlus 请求qwen-vl-plus进行图片识别
// imageURL: 图片URL地址
// customPrompt: 自定义提示词，如果为空则使用默认提示词
func (c *QwenVLClient) RequestQwenVLPlus(imageURL string, customPrompt string) (*QuestionResult, error) {
	// 默认系统提示词
	systemPrompt := "精准识别考题,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"题目正文\",\"options\":{\"选项标识\":\"选项内容\"}}"
	
	// 默认用户提示词
	userPrompt := "完整且精准的识别"
	if customPrompt != "" {
		userPrompt = customPrompt
	}

	// 构建请求体
	request := QwenVLRequest{
		Input: Input{
			Messages: []Message{
				{
					Content: []Content{
						{Text: systemPrompt},
					},
					Role: "system",
				},
				{
					Content: []Content{
						{Image: imageURL},
						{Text: userPrompt},
					},
					Role: "user",
				},
			},
		},
		Model: "qwen-vl-plus",
		Parameters: Parameters{
			PresencePenalty:   1.0,
			RepetitionPenalty: 1.0,
			ResponseFormat: ResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
		},
	}

	// 序列化请求体
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", c.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIKey)

	// 发送请求
	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResponse QwenVLResponse
	if err := json.Unmarshal(responseBody, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应是否有效
	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("响应中没有选择项")
	}

	// 获取生成的内容
	choice := qwenResponse.Output.Choices[0]
	if len(choice.Message.Content) == 0 {
		return nil, fmt.Errorf("响应消息内容为空")
	}

	// 解析题目识别结果
	var questionResult QuestionResult
	contentText := choice.Message.Content[0].Text
	if err := json.Unmarshal([]byte(contentText), &questionResult); err != nil {
		return nil, fmt.Errorf("解析题目识别结果失败: %v, 原始内容: %s", err, contentText)
	}

	return &questionResult, nil
}

// 示例使用方法
func main() {
	// 创建客户端实例
	client := NewQwenVLClient("sk-3920274bedf642c2b7495f534aadca84")

	// 请求图片识别
	imageURL := "http://img.igmdns.com/img/ca0726.jpg"
	result, err := client.RequestQwenVLPlus(imageURL, "")
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	// 打印结果
	fmt.Printf("识别结果:\n")
	fmt.Printf("题目类型: %s\n", result.Type)
	fmt.Printf("题目序号: %s\n", result.Num)
	fmt.Printf("题目正文: %s\n", result.Text)
	fmt.Printf("选项:\n")
	for key, value := range result.Options {
		fmt.Printf("  %s: %s\n", key, value)
	}
}
