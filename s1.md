使用go写一个请求qwen-vl-plus的业务。

将这个业务完整的封装成一个方法。后续我有其他地方使用。


DashScope模式请求；请求地址为；https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation

{
  "input": {
    "messages": [
      {
        "content": [
          {
            "text": "精准识别考题,严格标准返回json格式。示例{\"type\":\"问题类型\",\"num\":\"序号\",\"text\":\"题目正文\",\"options\":{\"选项标识\":\"选项内容\"}}"
          }
        ],
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/img/ca0726.jpg"
          },
          {
            "text": "完整且精准的识别"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "response_format": {
      "type": "json_object"
    },
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}



qwen的key为sk-3920274bedf642c2b7495f534aadca84