# Qwen-VL-Plus Go客户端 - 使用说明

## 项目概述

根据S1.md的需求，我已经完成了一个完整的Go语言客户端，用于请求阿里云DashScope的qwen-vl-plus多模态生成服务。该客户端专门用于识别图片中的考题并返回结构化的JSON数据。

## 文件结构

```
.
├── s1.md                   # 原始需求文档
├── qwen_vl_client.go       # 主要的客户端实现（完整版本）
├── qwen_client.go          # 简化的库文件
├── example_usage.go        # 详细使用示例
├── test_client.go          # 测试文件
├── go.mod                  # Go模块文件
├── README.md               # 详细说明文档
└── 使用说明.md             # 本文件
```

## 核心功能

### 1. 主要方法：RequestQwenVLPlus

这是根据需求完整封装的业务方法，可以在其他地方直接使用：

```go
func (c *QwenVLClient) RequestQwenVLPlus(imageURL string, customPrompt string) (*QuestionResult, error)
```

**参数说明：**
- `imageURL`: 图片URL地址
- `customPrompt`: 自定义提示词（可选，为空时使用默认提示词）

**返回值：**
- `*QuestionResult`: 识别结果结构体
- `error`: 错误信息

### 2. 数据结构

```go
type QuestionResult struct {
    Type    string            `json:"type"`    // 问题类型
    Num     string            `json:"num"`     // 序号
    Text    string            `json:"text"`    // 题目正文
    Options map[string]string `json:"options"` // 选项
}
```

## 快速使用

### 基本用法

```go
// 1. 创建客户端
client := NewQwenVLClient("your-api-key")

// 2. 识别图片
result, err := client.RequestQwenVLPlus("http://example.com/image.jpg", "")
if err != nil {
    log.Fatal(err)
}

// 3. 使用结果
fmt.Printf("题目类型: %s\n", result.Type)
fmt.Printf("题目内容: %s\n", result.Text)
```

### 在其他项目中使用

1. **复制核心代码**：将`qwen_client.go`中的结构体和方法复制到你的项目中
2. **导入依赖**：确保项目中有必要的import
3. **调用方法**：直接使用`RequestQwenVLPlus`方法

## 运行测试

```bash
# 编译测试
go build test_client.go

# 运行测试（需要有效的网络连接）
./test_client
```

## 配置说明

### API配置
- **API端点**: `https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation`
- **API密钥**: `sk-3920274bedf642c2b7495f534aadca84`（已在代码中配置）
- **模型**: `qwen-vl-plus`

### 请求参数
- **温度**: 0.2（较低随机性，更准确结果）
- **Top-K**: 1
- **Top-P**: 0.01
- **响应格式**: JSON对象
- **超时时间**: 30秒

## 实际应用场景

### 1. Web服务集成
```go
func handleImageRecognition(w http.ResponseWriter, r *http.Request) {
    client := NewQwenVLClient("your-api-key")
    imageURL := r.FormValue("image_url")
    
    result, err := client.RequestQwenVLPlus(imageURL, "")
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    json.NewEncoder(w).Encode(result)
}
```

### 2. 批量处理
```go
func processBatch(imageURLs []string) []*QuestionResult {
    client := NewQwenVLClient("your-api-key")
    var results []*QuestionResult
    
    for _, url := range imageURLs {
        result, err := client.RequestQwenVLPlus(url, "")
        if err != nil {
            log.Printf("处理失败: %v", err)
            continue
        }
        results = append(results, result)
    }
    
    return results
}
```

## 错误处理

客户端提供了完善的错误处理：
- 网络连接错误
- API认证错误
- JSON解析错误
- HTTP状态码错误
- 响应格式错误

## 注意事项

1. **API密钥安全**: 在生产环境中，请从环境变量或配置文件中读取API密钥
2. **网络连接**: 确保服务器能够访问外网
3. **图片URL**: 确保图片URL可以公开访问
4. **配额限制**: 注意API调用的配额和频率限制
5. **错误重试**: 建议在生产环境中添加重试机制

## 扩展功能

如需扩展功能，可以：
1. 添加更多的参数配置选项
2. 实现连接池管理
3. 添加缓存机制
4. 实现异步处理
5. 添加监控和日志

## 总结

该Go客户端完全满足S1.md中的需求：
- ✅ 完整封装成一个方法
- ✅ 支持DashScope模式请求
- ✅ 使用指定的API端点
- ✅ 包含完整的请求参数
- ✅ 支持图片识别和JSON格式返回
- ✅ 可以在其他地方直接使用

现在你可以直接使用`RequestQwenVLPlus`方法来处理图片识别业务了！
